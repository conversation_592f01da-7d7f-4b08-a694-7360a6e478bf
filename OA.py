import requests
import scrapy
import http.cookies
import ddddocr
import time
import re
import datetime
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup
import pandas as pd

ocr = ddddocr.DdddOcr()
s = requests.Session()
header = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36'
}


def get_oa_code():
    code_url = 'http://oa.wxatw.com/weaver/weaver.file.MakeValidateCode'
    resp = s.get(url=code_url, headers=header)
    res = ocr.classification(resp.content)
    print(res)
    return res


def oa_login(code):
    s.get('http://oa.wxatw.com/login/Login.jsp?logintype=1')
    url = 'http://oa.wxatw.com/login/VerifyLogin.jsp'
    data = {
        'loginfile': '/wui/theme/ecology8/page/login.jsp?templateId=21&logintype=1&gopage=',
        'logintype': 1,
        'formmethod': 'post',
        'isie': 'false',
        'islanguid': 7,
        'loginid': 'W1519',
        'userpassword': 'abcd1234.123',
        'validatecode': code,
        'submit': '登录'
    }
    time.sleep(0.5)
    resp = s.post(url=url, data=data, headers=header)


def get_meeting_list(before_days, after_days):
    date_list = []
    for i in range(-before_days, after_days + 1):
        date_list.append((datetime.datetime.now() +
                         datetime.timedelta(days=i)).strftime("%Y-%m-%d"))

    df_list = []
    oa_login(get_oa_code())
    for date in date_list:
        print('获取 %s 的数据' % date)
        # 获取tableString
        resp = s.get(
            'http://oa.wxatw.com/meeting/report/GetRoomMeetingList.jsp?datenow=%s' % date)
        try:
            key = re.findall(r"__tableStringKey__='(\w+)';", resp.text)[0]
        except:
            print(resp.text)
            break

        # 获取总页数及首页信息
        url = 'http://oa.wxatw.com/weaver/weaver.common.util.taglib.SplitPageXmlServlet'
        resp = s.post('%s?tableString=%s' % (url, key))
        root = ET.fromstring(resp.text)
        page_num = root.get('pagenum')

        info_list = []
        # 获取首页信息
        tmp_info = info_parser(root.iter('row'))
        info_list.extend(tmp_info)

        # 获取其他页数的信息
        for i in range(1, int(page_num)):
            resp = s.post(
                '%s?tableString=%s&pageIndex=%d&otype=DESC' % (url, key, i))
            root = ET.fromstring(resp.text)
            tmp_info = info_parser(root.iter('row'))
            info_list.extend(tmp_info)
            time.sleep(0.2)

        df = pd.DataFrame(
            columns=['会议ID', '会议名称', '召集人', '会议地点', '开始日期', '结束日期'], data=info_list)
        df_list.append([df, date])
    with pd.ExcelWriter("%s_%s会议列表.xlsx" % (date_list[0], date_list[-1])) as writer:
        for tmp_df in df_list:
            tmp_df[0].to_excel(writer, sheet_name=tmp_df[1])
            writer.save()


def info_parser(rows):
    data = []
    for row in rows:
        cols = row.findall('col')
        meeting_id = cols[0].attrib['showvalue']
        meeting_name = cols[1].attrib['value']
        soup = BeautifulSoup(cols[2].text, 'html.parser')
        person = soup.a.get_text()
        soup = BeautifulSoup(cols[4].text, 'html.parser')
        addr = soup.span.get_text()
        start_date = cols[5].text
        end_date = cols[6].text
        data.append([meeting_id, meeting_name, person,
                    addr, start_date, end_date])
    return data


if __name__ == '__main__':
    get_meeting_list(before_days=8, after_days=5)
