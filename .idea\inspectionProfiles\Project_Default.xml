<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="7">
            <item index="0" class="java.lang.String" itemvalue="ccxt" />
            <item index="1" class="java.lang.String" itemvalue="backtrader" />
            <item index="2" class="java.lang.String" itemvalue="SQLAlchemy" />
            <item index="3" class="java.lang.String" itemvalue="SQLAlchemy-Utils" />
            <item index="4" class="java.lang.String" itemvalue="akshare" />
            <item index="5" class="java.lang.String" itemvalue="binance" />
            <item index="6" class="java.lang.String" itemvalue="easyquotation" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N801" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>