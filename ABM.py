import requests
import scrapy
import http.cookies
import ddddocr
import time, re, datetime
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup
import pandas as pd
import execjs


def js_from_file(file_name):
    with open(file_name, 'r', encoding='UTF-8') as file:
        result = file.read()
    return result


js = execjs.compile(js_from_file('./md5.js'))

s = requests.Session()
header = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
}


def abm_login():
    url = 'http://**************:1010/login/doAction'
    data = {
        'city': "",
        'ip': "ILData[0]",
        'password': 'NhYzFTpGSDCh/oXexe+rFw',
        'remember': 'true',
        'W1519': "usercode",
    }
    time.sleep(0.5)
    resp = s.post(url=url, data=data, headers=header)
    resp = s.get('http://**************:1010/Home/Notes')
    print(resp.text)


if __name__ == '__main__':
    print(js.call("b64_md5", "W1519"))
    # abm_login()
